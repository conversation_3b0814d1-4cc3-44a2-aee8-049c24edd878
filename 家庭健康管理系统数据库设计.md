# 家庭健康管理系统数据库设计

## 1. 业务分析

### 核心实体识别
- **用户（User）**：应用的基础用户实体
- **家庭圈（Family）**：用户创建或加入的家庭组织
- **家庭成员（Family Member）**：家庭圈中的成员关系
- **健康档案（Health Profile）**：每个成员的基础健康信息
- **健康评估（Health Assessment）**：定期的健康评估记录

### 实体关系梳理
- 用户与家庭圈：多对多关系（一个用户可以属于多个家庭圈）
- 家庭圈与家庭成员：一对多关系
- 家庭成员与健康档案：一对一关系
- 家庭成员与健康评估：一对多关系

## 2. 表结构设计

### 2.1 用户表（rag_consumer）- 已存在

#### 现有表结构
```sql
CREATE TABLE `rag_consumer` (
  `id` bigint NOT NULL,
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `country_code` varchar(10) NOT NULL DEFAULT '' COMMENT '手机号国家码 如"+86"',
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `email` varchar(255) NOT NULL DEFAULT '' COMMENT '邮箱',
  `dingtalk_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `binding_user_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `app_user_id` varchar(20) DEFAULT NULL,
  `dingtalk_unionid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `device_id` varchar(255) DEFAULT NULL,
  `business_id` varchar(255) DEFAULT NULL,
  `introduction` json DEFAULT NULL,
  `source` varchar(255) DEFAULT NULL COMMENT '0-钉钉应用,1-食神H5,2-魔盒,3-ROKiAPP,4-admin后台,5-老板电器小程序,6-技师助手,7-海外版食神',
  `extend_data` json DEFAULT NULL COMMENT '扩展数据',
  `guid` varchar(255) DEFAULT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT '0',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `created_by` bigint unsigned DEFAULT NULL,
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_by` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

#### 字段说明
- `id`：主键，用户唯一标识
- `nickname`：用户昵称
- `avatar`：头像URL
- `country_code`：手机号国家码
- `phone`：手机号
- `email`：邮箱地址
- `source`：用户来源渠道
- `extend_data`：扩展数据，使用JSON格式存储灵活信息

### 2.2 家庭圈表（families）

#### 表结构
```sql
CREATE TABLE `families` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '家庭圈ID',
  `name` varchar(100) NOT NULL COMMENT '家庭圈名称',
  `description` varchar(500) DEFAULT NULL COMMENT '家庭圈描述',
  `avatar_url` varchar(500) DEFAULT NULL COMMENT '家庭圈头像',
  `creator_id` bigint NOT NULL COMMENT '创建者ID，关联rag_consumer.id',
  `member_count` int unsigned NOT NULL DEFAULT '1' COMMENT '成员数量',
  `max_member_count` int unsigned NOT NULL DEFAULT '20' COMMENT '最大成员数量',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-正常，2-禁用',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `created_by` bigint unsigned NOT NULL COMMENT '创建人ID',
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `updated_by` bigint unsigned NOT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_families_creator` FOREIGN KEY (`creator_id`) REFERENCES `rag_consumer` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='家庭圈表';
```

#### 字段说明
- `id`：主键，家庭圈唯一标识
- `name`：家庭圈名称，如"我的家庭"
- `description`：家庭圈描述信息
- `creator_id`：创建者ID，关联现有用户表
- `member_count`：当前成员数量，冗余字段提升查询性能
- `max_member_count`：最大成员数量限制

## 3. 索引策略

### 用户表（rag_consumer）索引
- **主键索引**：`id`（聚簇索引）
- **建议添加索引**：
  - `phone`（登录查询优化）
  - `email`（邮箱登录查询）
  - `app_user_id`（应用用户查询）

### 家庭圈表（families）索引
- **主键索引**：`id`（聚簇索引）
- **普通索引**：
  - `creator_id`（创建者查询）
  - `status`（状态筛选）
  - `created_at`（时间排序）

### 家庭成员表（family_members）索引
- **主键索引**：`id`（聚簇索引）
- **唯一索引**：`uk_family_user`（family_id, user_id）防止重复加入
- **普通索引**：
  - `family_id`（家庭圈成员查询）
  - `user_id`（用户家庭查询）
  - `relationship`（关系筛选）
  - `role`（角色权限查询）
  - `status`（状态筛选）

## 4. SQL示例

### 创建家庭圈表
```sql
-- 家庭圈表创建语句（见上述表结构）
```

### 常用查询示例
```sql
-- 根据手机号查询用户
SELECT * FROM rag_consumer WHERE phone = '13800138000' AND is_deleted = 0;

-- 查询用户创建的家庭圈
SELECT f.* FROM families f
WHERE f.creator_id = 123456 AND f.is_deleted = 0
ORDER BY f.created_at DESC;

-- 查询家庭圈所有成员
SELECT fm.id, fm.member_name, fm.relationship, fm.gender, fm.role,
       rc.nickname, rc.avatar
FROM family_members fm
LEFT JOIN rag_consumer rc ON fm.user_id = rc.id
WHERE fm.family_id = 1 AND fm.is_deleted = 0
ORDER BY fm.role DESC, fm.join_time ASC;

-- 查询用户参与的所有家庭圈
SELECT f.id, f.name, f.avatar_url, fm.relationship, fm.role
FROM families f
INNER JOIN family_members fm ON f.id = fm.family_id
WHERE fm.user_id = 123456 AND f.is_deleted = 0 AND fm.is_deleted = 0
ORDER BY fm.join_time DESC;

-- 查询家庭圈中的管理员
SELECT fm.*, rc.nickname, rc.phone
FROM family_members fm
LEFT JOIN rag_consumer rc ON fm.user_id = rc.id
WHERE fm.family_id = 1 AND fm.role >= 2 AND fm.is_deleted = 0;

-- 统计各家庭圈的成员数量
SELECT f.id, f.name, COUNT(fm.id) as actual_member_count, f.member_count
FROM families f
LEFT JOIN family_members fm ON f.id = fm.family_id AND fm.is_deleted = 0
WHERE f.is_deleted = 0
GROUP BY f.id, f.name, f.member_count;
```

## 5. 扩展性考虑

### 用户表（rag_consumer）扩展性
- **多渠道支持**：已支持钉钉、H5、小程序等多种来源
- **扩展数据**：使用JSON字段存储灵活的扩展信息
- **国际化支持**：支持国家码，便于海外用户接入
- **第三方集成**：已预留钉钉等第三方平台集成字段

### 家庭圈表扩展性
- **成员管理**：支持最大成员数量限制和动态调整
- **权限扩展**：预留状态字段支持更多权限控制
- **多媒体支持**：支持家庭圈头像等多媒体内容
- **描述信息**：支持家庭圈详细描述和个性化设置

### 性能优化预留
- 使用bigint主键支持海量数据
- 合理的索引设计平衡查询性能和存储空间
- 软删除机制保证数据安全性
- 外键约束保证数据完整性

### 2.3 家庭成员表（family_members）

#### 表结构
```sql
CREATE TABLE `family_members` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '家庭成员ID',
  `family_id` bigint unsigned NOT NULL COMMENT '家庭圈ID，关联families.id',
  `user_id` bigint DEFAULT NULL COMMENT '关联用户ID，关联rag_consumer.id，可为空（虚拟成员）',
  `member_name` varchar(50) NOT NULL COMMENT '成员姓名',
  `member_avatar` varchar(500) DEFAULT NULL COMMENT '成员头像',
  `relationship` varchar(20) NOT NULL COMMENT '家庭关系：本人、爸爸、妈妈、儿子、女儿、爷爷、奶奶、外公、外婆等',
  `gender` tinyint DEFAULT NULL COMMENT '性别：1-男，2-女',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `is_primary` tinyint NOT NULL DEFAULT '0' COMMENT '是否为主要成员：0-否，1-是',
  `role` tinyint NOT NULL DEFAULT '1' COMMENT '角色：1-普通成员，2-管理员，3-创建者',
  `join_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '加入时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-正常，2-禁用',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `created_by` bigint unsigned NOT NULL COMMENT '创建人ID',
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `updated_by` bigint unsigned NOT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_family_user` (`family_id`, `user_id`),
  KEY `idx_family_id` (`family_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_relationship` (`relationship`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_family_members_family` FOREIGN KEY (`family_id`) REFERENCES `families` (`id`),
  CONSTRAINT `fk_family_members_user` FOREIGN KEY (`user_id`) REFERENCES `rag_consumer` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='家庭成员表';
```

#### 字段说明
- `id`：主键，家庭成员唯一标识
- `family_id`：所属家庭圈ID
- `user_id`：关联的应用用户ID，可为空（支持虚拟成员）
- `member_name`：成员姓名，如"橘子钙片"、"爸爸"、"妈妈"
- `relationship`：家庭关系，支持中文描述
- `is_primary`：是否为主要成员（创建家庭圈的用户）
- `role`：成员角色，支持权限管理
- `join_time`：加入家庭圈的时间

## 6. 下一步设计计划

### 待设计表结构
1. **健康档案表（health_profiles）** - 成员基础健康信息
2. **健康评估表（health_assessments）** - 定期健康评估记录
3. **健康指标表（health_metrics）** - 具体健康指标数据

---

**状态：已完成用户表分析、家庭圈表和家庭成员表设计，等待审查确认后继续设计健康档案表**
